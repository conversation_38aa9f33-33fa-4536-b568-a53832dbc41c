#!/usr/bin/env python3
"""
Exemple d'utilisation du COBIT Analyzer
Script de démonstration pour l'analyse des objectifs COBIT 2019
"""

from cobit_analyzer import COBITAnalyzer
from cobit_tech_mapping import get_category_stats, TECH_CATEGORIES_LIST
import os

def run_complete_analysis():
    """Exécute une analyse complète des objectifs COBIT"""
    
    print("🚀 COBIT 2019 - Analyse des Objectifs Technologiques")
    print("=" * 60)
    
    # Initialiser l'analyseur
    analyzer = COBITAnalyzer()
    
    # Vérifier que le fichier de données existe
    data_file = "cobit_data.csv"
    if not os.path.exists(data_file):
        print(f"❌ Erreur: Le fichier {data_file} n'existe pas")
        print("💡 Assurez-vous d'avoir un fichier CSV avec les colonnes:")
        print("   - Objectif (ex: APO03, DSS01, etc.)")
        print("   - Score (score personnalisé)")
        print("   - Baseline (score de référence)")
        print("   - Relative_Importance (optionnel, sera calculé si absent)")
        return
    
    # Analyser les données
    print(f"📂 Chargement des données depuis: {data_file}")
    results = analyzer.analyze_cobit_objectives(data_file)
    
    if not results:
        print("❌ Échec de l'analyse")
        return
    
    # Afficher les statistiques générales
    print(f"\n📊 STATISTIQUES GÉNÉRALES")
    print("-" * 30)
    total_objectives = len(results['all_objectives'])
    retained_count = len(results['retained_all'])
    tech_retained_count = len(results['tech_retained'])
    tech_ignored_count = len(results['tech_ignored'])
    
    print(f"Total objectifs analysés: {total_objectives}")
    print(f"Objectifs retenus (≥0): {retained_count}")
    print(f"Objectifs technologiques retenus: {tech_retained_count}")
    print(f"Objectifs technologiques ignorés: {tech_ignored_count}")
    
    # Générer le rapport détaillé
    analyzer.generate_summary_report()
    
    # Afficher les catégories technologiques disponibles
    print(f"\n🔧 CATÉGORIES TECHNOLOGIQUES DISPONIBLES")
    print("-" * 45)
    category_stats = get_category_stats()
    for category, count in category_stats.items():
        print(f"  • {category}: {count} objectifs définis")
    
    # Créer la visualisation
    print(f"\n📊 GÉNÉRATION DU GRAPHIQUE")
    print("-" * 30)
    try:
        analyzer.create_visualization("cobit_tech_analysis.png")
    except Exception as e:
        print(f"⚠️  Erreur lors de la création du graphique: {e}")
        print("💡 Assurez-vous d'avoir matplotlib installé: pip install matplotlib seaborn")
    
    # Exporter les résultats
    print(f"\n💾 EXPORT DES RÉSULTATS")
    print("-" * 25)
    analyzer.export_results("results")
    
    print(f"\n✅ ANALYSE TERMINÉE")
    print("=" * 60)
    print("📁 Fichiers générés:")
    print("  • results/objectifs_tech_retenus.csv")
    print("  • results/objectifs_tech_ignores.csv") 
    print("  • cobit_tech_analysis.png (si matplotlib disponible)")

def quick_analysis_example():
    """Exemple d'analyse rapide avec données personnalisées"""
    
    print("\n🔍 EXEMPLE D'ANALYSE RAPIDE")
    print("=" * 40)
    
    # Créer des données d'exemple en mémoire
    import pandas as pd
    
    sample_data = {
        'Objectif': ['APO03', 'APO04', 'DSS05', 'BAI08', 'EDM01', 'MEA01'],
        'Score': [4.1, 3.8, 4.0, 4.0, 3.2, 3.8],
        'Baseline': [3.0, 2.8, 2.9, 3.0, 3.0, 2.9],
        'Relative_Importance': [1.1, 1.0, 1.1, 1.0, 0.2, 0.9]
    }
    
    df = pd.DataFrame(sample_data)
    
    # Sauvegarder temporairement
    temp_file = "temp_cobit_data.csv"
    df.to_csv(temp_file, index=False)
    
    # Analyser
    analyzer = COBITAnalyzer()
    results = analyzer.analyze_cobit_objectives(temp_file)
    
    if results:
        analyzer.generate_summary_report()
    
    # Nettoyer
    if os.path.exists(temp_file):
        os.remove(temp_file)

def main():
    """Fonction principale"""
    
    # Analyse complète
    run_complete_analysis()
    
    # Exemple rapide
    quick_analysis_example()
    
    print(f"\n💡 CONSEILS D'UTILISATION")
    print("-" * 25)
    print("1. Modifiez cobit_data.csv avec vos propres données")
    print("2. Ajustez le mapping technologique dans cobit_tech_mapping.py")
    print("3. Personnalisez les seuils d'importance dans cobit_analyzer.py")
    print("4. Installez les dépendances: pip install pandas matplotlib seaborn")

if __name__ == "__main__":
    main()
