"""
Configuration du mapping des objectifs COBIT 2019 vers les catégories technologiques
Expert en gouvernance IT - Classification technologique COBIT
"""

# Mapping des objectifs COBIT vers les catégories technologiques
COBIT_TECH_CATEGORIES = {
    # ========================================
    # ARCHITECTURE IT
    # ========================================
    'APO03': {
        'category': 'Architecture IT',
        'description': 'Gérer l\'architecture d\'entreprise',
        'focus': 'Architecture et conception des systèmes'
    },
    'APO12': {
        'category': 'Architecture IT', 
        'description': 'Gérer les risques',
        'focus': 'Architecture de sécurité et gestion des risques IT'
    },
    
    # ========================================
    # INNOVATION
    # ========================================
    'APO04': {
        'category': 'Innovation',
        'description': 'Gérer l\'innovation',
        'focus': 'Innovation technologique et transformation digitale'
    },
    'BAI06': {
        'category': 'Innovation',
        'description': 'Gérer les changements',
        'focus': 'Innovation dans les processus et technologies'
    },
    'BAI07': {
        'category': 'Innovation',
        'description': 'Gérer l\'acceptation du changement et la transition',
        'focus': 'Adoption de nouvelles technologies'
    },
    
    # ========================================
    # AUTOMATISATION / PERFORMANCE
    # ========================================
    'BAI08': {
        'category': 'Automatisation / Performance',
        'description': 'Gérer les connaissances',
        'focus': 'Automatisation des processus et optimisation'
    },
    'MEA01': {
        'category': 'Automatisation / Performance',
        'description': 'Surveiller, évaluer et apprécier la performance et la conformité',
        'focus': 'Monitoring automatisé et métriques de performance'
    },
    'MEA02': {
        'category': 'Automatisation / Performance',
        'description': 'Surveiller, évaluer et apprécier le système de contrôle interne',
        'focus': 'Contrôles automatisés et performance système'
    },
    
    # ========================================
    # INFRASTRUCTURE / EXPLOITATION
    # ========================================
    'DSS01': {
        'category': 'Infrastructure / Exploitation',
        'description': 'Gérer les opérations',
        'focus': 'Exploitation et maintenance de l\'infrastructure IT'
    },
    'DSS02': {
        'category': 'Infrastructure / Exploitation',
        'description': 'Gérer les demandes de service et les incidents',
        'focus': 'Gestion des services et support technique'
    },
    'DSS03': {
        'category': 'Infrastructure / Exploitation',
        'description': 'Gérer les problèmes',
        'focus': 'Résolution des problèmes d\'infrastructure'
    },
    'BAI10': {
        'category': 'Infrastructure / Exploitation',
        'description': 'Gérer la configuration',
        'focus': 'Configuration et déploiement d\'infrastructure'
    },
    
    # ========================================
    # CYBERSÉCURITÉ
    # ========================================
    'DSS04': {
        'category': 'Cybersécurité',
        'description': 'Gérer la continuité',
        'focus': 'Continuité des activités et sécurité'
    },
    'DSS05': {
        'category': 'Cybersécurité',
        'description': 'Gérer les services de sécurité',
        'focus': 'Services et technologies de cybersécurité'
    },
    'DSS06': {
        'category': 'Cybersécurité',
        'description': 'Gérer les contrôles des processus métier',
        'focus': 'Contrôles de sécurité des processus'
    },
    'APO13': {
        'category': 'Cybersécurité',
        'description': 'Gérer la sécurité',
        'focus': 'Stratégie et gouvernance de la cybersécurité'
    },
    
    # ========================================
    # ÉVALUATION TECHNOLOGIQUE
    # ========================================
    'MEA03': {
        'category': 'Évaluation technologique',
        'description': 'Surveiller, évaluer et apprécier la conformité aux exigences externes',
        'focus': 'Évaluation de la conformité technologique'
    },
    'BAI01': {
        'category': 'Évaluation technologique',
        'description': 'Gérer les programmes et les projets',
        'focus': 'Évaluation et sélection de technologies'
    },
    'BAI02': {
        'category': 'Évaluation technologique',
        'description': 'Gérer la définition des exigences',
        'focus': 'Analyse et évaluation des besoins technologiques'
    },
    'BAI03': {
        'category': 'Évaluation technologique',
        'description': 'Gérer l\'identification et la construction de solutions',
        'focus': 'Évaluation et développement de solutions techniques'
    }
}

# Liste des catégories technologiques disponibles
TECH_CATEGORIES_LIST = [
    'Architecture IT',
    'Innovation', 
    'Automatisation / Performance',
    'Infrastructure / Exploitation',
    'Cybersécurité',
    'Évaluation technologique'
]

# Mapping simplifié pour utilisation rapide
SIMPLE_TECH_MAPPING = {obj: info['category'] for obj, info in COBIT_TECH_CATEGORIES.items()}

def get_tech_category(objective_code: str) -> str:
    """
    Retourne la catégorie technologique d'un objectif COBIT
    
    Args:
        objective_code: Code de l'objectif COBIT (ex: 'APO03')
        
    Returns:
        Nom de la catégorie technologique ou None si non technologique
    """
    return SIMPLE_TECH_MAPPING.get(objective_code)

def get_tech_description(objective_code: str) -> dict:
    """
    Retourne les informations détaillées d'un objectif technologique
    
    Args:
        objective_code: Code de l'objectif COBIT
        
    Returns:
        Dictionnaire avec category, description, focus
    """
    return COBIT_TECH_CATEGORIES.get(objective_code)

def list_objectives_by_category(category: str) -> list:
    """
    Liste tous les objectifs d'une catégorie technologique donnée
    
    Args:
        category: Nom de la catégorie technologique
        
    Returns:
        Liste des codes d'objectifs COBIT
    """
    return [obj for obj, info in COBIT_TECH_CATEGORIES.items() 
            if info['category'] == category]

def get_all_tech_objectives() -> list:
    """
    Retourne la liste de tous les objectifs technologiques
    
    Returns:
        Liste des codes d'objectifs COBIT technologiques
    """
    return list(COBIT_TECH_CATEGORIES.keys())

# Statistiques des catégories
def get_category_stats() -> dict:
    """
    Retourne les statistiques par catégorie technologique
    
    Returns:
        Dictionnaire avec le nombre d'objectifs par catégorie
    """
    stats = {}
    for category in TECH_CATEGORIES_LIST:
        stats[category] = len(list_objectives_by_category(category))
    return stats

if __name__ == "__main__":
    # Test du mapping
    print("🔧 COBIT 2019 - Mapping Technologique")
    print("=" * 50)
    
    print(f"\n📊 Nombre total d'objectifs technologiques: {len(COBIT_TECH_CATEGORIES)}")
    
    print(f"\n📋 Statistiques par catégorie:")
    for category, count in get_category_stats().items():
        print(f"  • {category}: {count} objectifs")
    
    print(f"\n🔍 Exemple - APO03:")
    info = get_tech_description('APO03')
    if info:
        print(f"  Catégorie: {info['category']}")
        print(f"  Description: {info['description']}")
        print(f"  Focus: {info['focus']}")
