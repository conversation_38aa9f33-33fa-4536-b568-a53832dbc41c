#!/usr/bin/env python3
"""
COBIT 2019 Technology Objectives Analyzer
Expert en gouvernance IT - Analyse des objectifs technologiques COBIT

Ce script analyse les objectifs COBIT 2019 pour identifier ceux liés à la technologie
et les classe selon leur importance relative.
"""

import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from typing import Dict, List, Tuple
import os

class COBITAnalyzer:
    """Analyseur des objectifs COBIT 2019 orientés technologie"""
    
    def __init__(self):
        """Initialise l'analyseur avec le mapping des catégories technologiques"""
        self.tech_categories = {
            # Architecture IT
            'APO03': 'Architecture IT',
            'APO12': 'Architecture IT',
            
            # Innovation
            'APO04': 'Innovation',
            'BAI06': 'Innovation',
            'BAI07': 'Innovation',
            
            # Automatisation / Performance
            'BAI08': 'Automatisation / Performance',
            'MEA01': 'Automatisation / Performance',
            'MEA02': 'Automatisation / Performance',
            
            # Infrastructure / Exploitation
            'DSS01': 'Infrastructure / Exploitation',
            'DSS03': 'Infrastructure / Exploitation',
            'DSS02': 'Infrastructure / Exploitation',
            'BAI10': 'Infrastructure / Exploitation',
            
            # Cybersécurité
            'DSS04': 'Cybersécurité',
            'DSS05': 'Cybersécurité',
            'DSS06': 'Cybersécurité',
            'APO13': 'Cybersécurité',
            
            # Évaluation technologique
            'MEA03': 'Évaluation technologique',
            'BAI01': 'Évaluation technologique',
            'BAI02': 'Évaluation technologique',
            'BAI03': 'Évaluation technologique'
        }
        
        self.results = {}
    
    def load_data(self, file_path: str) -> pd.DataFrame:
        """
        Charge les données COBIT depuis un fichier CSV
        
        Args:
            file_path: Chemin vers le fichier CSV
            
        Returns:
            DataFrame avec les données COBIT
        """
        try:
            df = pd.read_csv(file_path)
            print(f"✅ Données chargées: {len(df)} objectifs COBIT")
            return df
        except FileNotFoundError:
            print(f"❌ Erreur: Fichier {file_path} non trouvé")
            return pd.DataFrame()
        except Exception as e:
            print(f"❌ Erreur lors du chargement: {e}")
            return pd.DataFrame()
    
    def calculate_relative_importance(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Calcule la Relative Importance si elle n'existe pas
        
        Args:
            df: DataFrame avec les colonnes Score et Baseline
            
        Returns:
            DataFrame avec la colonne Relative_Importance ajoutée
        """
        if 'Relative_Importance' not in df.columns:
            df['Relative_Importance'] = df['Score'] - df['Baseline']
            print("✅ Relative Importance calculée")
        
        return df
    
    def filter_by_importance(self, df: pd.DataFrame, threshold: float = 0) -> Tuple[pd.DataFrame, pd.DataFrame]:
        """
        Filtre les objectifs selon leur importance relative
        
        Args:
            df: DataFrame des objectifs COBIT
            threshold: Seuil de relative importance (défaut: 0)
            
        Returns:
            Tuple (objectifs_retenus, objectifs_ignores)
        """
        retained = df[df['Relative_Importance'] >= threshold].copy()
        ignored = df[df['Relative_Importance'] < threshold].copy()
        
        print(f"📊 Objectifs retenus (≥{threshold}): {len(retained)}")
        print(f"📊 Objectifs ignorés (<{threshold}): {len(ignored)}")
        
        return retained, ignored
    
    def classify_tech_objectives(self, df: pd.DataFrame) -> Tuple[pd.DataFrame, pd.DataFrame]:
        """
        Classe les objectifs selon les catégories technologiques
        
        Args:
            df: DataFrame des objectifs COBIT
            
        Returns:
            Tuple (objectifs_tech, objectifs_non_tech)
        """
        # Ajouter la catégorie technologique
        df['Tech_Category'] = df['Objectif'].map(self.tech_categories)
        
        # Séparer les objectifs technologiques des autres
        tech_objectives = df[df['Tech_Category'].notna()].copy()
        non_tech_objectives = df[df['Tech_Category'].isna()].copy()
        
        print(f"🔧 Objectifs technologiques identifiés: {len(tech_objectives)}")
        print(f"📋 Objectifs non-technologiques: {len(non_tech_objectives)}")
        
        return tech_objectives, non_tech_objectives
    
    def analyze_cobit_objectives(self, file_path: str) -> Dict:
        """
        Analyse complète des objectifs COBIT
        
        Args:
            file_path: Chemin vers le fichier de données
            
        Returns:
            Dictionnaire avec tous les résultats d'analyse
        """
        print("🚀 Début de l'analyse COBIT 2019")
        print("=" * 50)
        
        # 1. Charger les données
        df = self.load_data(file_path)
        if df.empty:
            return {}
        
        # 2. Calculer la relative importance si nécessaire
        df = self.calculate_relative_importance(df)
        
        # 3. Filtrer par importance relative
        retained_all, ignored_all = self.filter_by_importance(df)
        
        # 4. Classifier les objectifs technologiques parmi ceux retenus
        tech_retained, non_tech_retained = self.classify_tech_objectives(retained_all)
        
        # 5. Classifier les objectifs technologiques parmi ceux ignorés
        tech_ignored, _ = self.classify_tech_objectives(ignored_all)
        
        # Stocker les résultats
        self.results = {
            'all_objectives': df,
            'retained_all': retained_all,
            'ignored_all': ignored_all,
            'tech_retained': tech_retained,
            'tech_ignored': tech_ignored,
            'non_tech_retained': non_tech_retained
        }
        
        return self.results
    
    def generate_summary_report(self) -> None:
        """Génère un rapport de synthèse"""
        if not self.results:
            print("❌ Aucune analyse disponible. Exécutez d'abord analyze_cobit_objectives()")
            return
        
        print("\n📋 RAPPORT DE SYNTHÈSE COBIT 2019")
        print("=" * 50)
        
        tech_retained = self.results['tech_retained']
        tech_ignored = self.results['tech_ignored']
        
        print(f"\n✅ OBJECTIFS TECHNOLOGIQUES À RETENIR ({len(tech_retained)}):")
        if not tech_retained.empty:
            # Trier par importance relative décroissante
            tech_retained_sorted = tech_retained.sort_values('Relative_Importance', ascending=False)
            for _, row in tech_retained_sorted.iterrows():
                print(f"  • {row['Objectif']} | Importance: {row['Relative_Importance']:.2f} | {row['Tech_Category']}")
        else:
            print("  Aucun objectif technologique à retenir")
        
        print(f"\n❌ OBJECTIFS TECHNOLOGIQUES À IGNORER ({len(tech_ignored)}):")
        if not tech_ignored.empty:
            tech_ignored_sorted = tech_ignored.sort_values('Relative_Importance', ascending=True)
            for _, row in tech_ignored_sorted.iterrows():
                print(f"  • {row['Objectif']} | Importance: {row['Relative_Importance']:.2f} | {row['Tech_Category']}")
        else:
            print("  Aucun objectif technologique à ignorer")
        
        # Statistiques par catégorie
        print(f"\n📊 RÉPARTITION PAR CATÉGORIE TECHNOLOGIQUE:")
        if not tech_retained.empty:
            category_stats = tech_retained['Tech_Category'].value_counts()
            for category, count in category_stats.items():
                print(f"  • {category}: {count} objectif(s)")
    
    def create_visualization(self, save_path: str = None) -> None:
        """
        Crée un graphique horizontal des objectifs technologiques retenus
        
        Args:
            save_path: Chemin pour sauvegarder le graphique (optionnel)
        """
        if not self.results or self.results['tech_retained'].empty:
            print("❌ Aucun objectif technologique à visualiser")
            return
        
        tech_retained = self.results['tech_retained'].copy()
        tech_retained_sorted = tech_retained.sort_values('Relative_Importance', ascending=True)
        
        # Configuration du style
        plt.style.use('seaborn-v0_8')
        fig, ax = plt.subplots(figsize=(12, 8))
        
        # Créer le graphique horizontal
        colors = plt.cm.viridis(range(len(tech_retained_sorted)))
        bars = ax.barh(tech_retained_sorted['Objectif'], 
                      tech_retained_sorted['Relative_Importance'],
                      color=colors)
        
        # Personnalisation
        ax.set_xlabel('Relative Importance', fontsize=12, fontweight='bold')
        ax.set_ylabel('Objectifs COBIT', fontsize=12, fontweight='bold')
        ax.set_title('Objectifs COBIT 2019 - Technologiques Retenus\n(Triés par Importance Relative)', 
                    fontsize=14, fontweight='bold', pad=20)
        
        # Ajouter les valeurs sur les barres
        for i, (bar, value) in enumerate(zip(bars, tech_retained_sorted['Relative_Importance'])):
            ax.text(value + 0.01, bar.get_y() + bar.get_height()/2, 
                   f'{value:.2f}', va='center', fontsize=10)
        
        # Ajouter une grille
        ax.grid(axis='x', alpha=0.3)
        
        # Ajuster la mise en page
        plt.tight_layout()
        
        # Sauvegarder si demandé
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"📊 Graphique sauvegardé: {save_path}")
        
        plt.show()
    
    def export_results(self, output_dir: str = "results") -> None:
        """
        Exporte les résultats vers des fichiers CSV
        
        Args:
            output_dir: Répertoire de sortie
        """
        if not self.results:
            print("❌ Aucune analyse disponible")
            return
        
        # Créer le répertoire de sortie
        os.makedirs(output_dir, exist_ok=True)
        
        # Exporter les objectifs technologiques retenus
        tech_retained = self.results['tech_retained']
        if not tech_retained.empty:
            output_file = os.path.join(output_dir, "objectifs_tech_retenus.csv")
            tech_retained_export = tech_retained[['Objectif', 'Score', 'Baseline', 'Relative_Importance', 'Tech_Category']]
            tech_retained_export.to_csv(output_file, index=False, encoding='utf-8')
            print(f"📁 Objectifs retenus exportés: {output_file}")
        
        # Exporter les objectifs technologiques ignorés
        tech_ignored = self.results['tech_ignored']
        if not tech_ignored.empty:
            output_file = os.path.join(output_dir, "objectifs_tech_ignores.csv")
            tech_ignored_export = tech_ignored[['Objectif', 'Score', 'Baseline', 'Relative_Importance', 'Tech_Category']]
            tech_ignored_export.to_csv(output_file, index=False, encoding='utf-8')
            print(f"📁 Objectifs ignorés exportés: {output_file}")


def main():
    """Fonction principale d'exemple"""
    analyzer = COBITAnalyzer()
    
    # Analyser les données (remplacez par votre fichier)
    results = analyzer.analyze_cobit_objectives("cobit_data.csv")
    
    if results:
        # Générer le rapport
        analyzer.generate_summary_report()
        
        # Créer la visualisation
        analyzer.create_visualization("cobit_analysis.png")
        
        # Exporter les résultats
        analyzer.export_results()


if __name__ == "__main__":
    main()
