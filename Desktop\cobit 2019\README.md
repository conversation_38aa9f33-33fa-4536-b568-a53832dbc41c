# COBIT 2019 - Analyseur d'Objectifs Technologiques

## 🎯 Description

Cet outil analyse les objectifs COBIT 2019 pour identifier et classer ceux liés à la technologie selon leur importance relative. Il est conçu par un expert en gouvernance IT pour faciliter la priorisation des objectifs technologiques.

## 🔧 Fonctionnalités

- ✅ **Filtrage par importance relative** : Identifie les objectifs avec Relative Importance ≥ 0
- 🏷️ **Classification technologique** : Classe les objectifs en 6 catégories technologiques
- 📊 **Visualisation** : Génère des graphiques horizontaux des objectifs retenus
- 📁 **Export** : Sauvegarde les résultats en fichiers CSV
- 📋 **Rapport détaillé** : Génère un rapport de synthèse complet

## 📂 Structure du Projet

```
cobit 2019/
├── cobit_analyzer.py          # Script principal d'analyse
├── cobit_tech_mapping.py      # Configuration des catégories technologiques
├── example_usage.py           # Exemple d'utilisation
├── cobit_data.csv            # Données d'exemple
├── requirements.txt          # Dépendances Python
└── README.md                 # Ce fichier
```

## 🚀 Installation

### 1. Installer Python

**Option A - Microsoft Store (Recommandé)**
1. Ouvrir le Microsoft Store
2. Rechercher "Python 3.11" ou "Python 3.12"
3. Cliquer sur "Installer"

**Option B - Site officiel**
1. Aller sur https://www.python.org/downloads/
2. Télécharger Python 3.11+ pour Windows
3. Exécuter l'installateur et cocher "Add Python to PATH"

### 2. Installer les dépendances

Ouvrir un terminal dans le dossier du projet et exécuter :

```bash
pip install -r requirements.txt
```

Ou installer manuellement :

```bash
pip install pandas matplotlib seaborn numpy
```

## 📊 Format des Données

Votre fichier CSV doit contenir les colonnes suivantes :

| Colonne | Description | Exemple |
|---------|-------------|---------|
| `Objectif` | Code COBIT | APO03, DSS05, BAI08 |
| `Score` | Score personnalisé | 4.1, 3.8, 2.9 |
| `Baseline` | Score de référence | 3.0, 2.8, 3.1 |
| `Relative_Importance` | Score - Baseline (optionnel) | 1.1, 1.0, -0.2 |

**Exemple de fichier CSV :**
```csv
Objectif,Score,Baseline,Relative_Importance
APO03,4.1,3.0,1.1
DSS05,4.0,2.9,1.1
BAI08,4.0,3.0,1.0
EDM01,3.2,3.0,0.2
APO02,2.7,3.2,-0.5
```

## 🏷️ Catégories Technologiques

L'outil classe les objectifs COBIT en 6 catégories technologiques :

### 🏗️ Architecture IT
- **APO03** : Gérer l'architecture d'entreprise
- **APO12** : Gérer les risques (architecture de sécurité)

### 💡 Innovation
- **APO04** : Gérer l'innovation
- **BAI06** : Gérer les changements
- **BAI07** : Gérer l'acceptation du changement

### ⚡ Automatisation / Performance
- **BAI08** : Gérer les connaissances
- **MEA01** : Surveiller la performance et conformité
- **MEA02** : Surveiller le système de contrôle interne

### 🖥️ Infrastructure / Exploitation
- **DSS01** : Gérer les opérations
- **DSS02** : Gérer les demandes de service
- **DSS03** : Gérer les problèmes
- **BAI10** : Gérer la configuration

### 🔒 Cybersécurité
- **DSS04** : Gérer la continuité
- **DSS05** : Gérer les services de sécurité
- **DSS06** : Gérer les contrôles des processus
- **APO13** : Gérer la sécurité

### 🔍 Évaluation technologique
- **MEA03** : Surveiller la conformité externe
- **BAI01** : Gérer les programmes et projets
- **BAI02** : Gérer la définition des exigences
- **BAI03** : Gérer l'identification de solutions

## 🚀 Utilisation

### Utilisation Simple

```python
from cobit_analyzer import COBITAnalyzer

# Initialiser l'analyseur
analyzer = COBITAnalyzer()

# Analyser vos données
results = analyzer.analyze_cobit_objectives("votre_fichier.csv")

# Générer le rapport
analyzer.generate_summary_report()

# Créer la visualisation
analyzer.create_visualization("graphique.png")

# Exporter les résultats
analyzer.export_results("dossier_resultats")
```

### Exécution avec l'exemple

```bash
python example_usage.py
```

### Utilisation Avancée

```python
from cobit_analyzer import COBITAnalyzer
from cobit_tech_mapping import get_tech_category, TECH_CATEGORIES_LIST

analyzer = COBITAnalyzer()

# Analyser avec seuil personnalisé
results = analyzer.analyze_cobit_objectives("data.csv")

# Accéder aux résultats détaillés
tech_retained = results['tech_retained']
tech_ignored = results['tech_ignored']

# Filtrer par catégorie spécifique
cybersecurity_objectives = tech_retained[
    tech_retained['Tech_Category'] == 'Cybersécurité'
]
```

## 📁 Fichiers de Sortie

L'outil génère automatiquement :

1. **results/objectifs_tech_retenus.csv** : Objectifs technologiques à retenir
2. **results/objectifs_tech_ignores.csv** : Objectifs technologiques à ignorer  
3. **cobit_tech_analysis.png** : Graphique des objectifs retenus
4. **Rapport console** : Synthèse détaillée affichée

## 🎨 Personnalisation

### Modifier les Catégories Technologiques

Éditez le fichier `cobit_tech_mapping.py` pour :
- Ajouter de nouveaux objectifs technologiques
- Modifier les catégories existantes
- Créer de nouvelles catégories

### Ajuster les Seuils

Dans `cobit_analyzer.py`, modifiez la méthode `filter_by_importance()` pour changer le seuil de relative importance.

### Personnaliser les Graphiques

Modifiez la méthode `create_visualization()` pour :
- Changer les couleurs
- Ajuster la taille
- Modifier le style

## 🔧 Dépannage

### Erreur "Python introuvable"
- Installer Python depuis le Microsoft Store ou python.org
- Vérifier que Python est dans le PATH

### Erreur "Module not found"
```bash
pip install pandas matplotlib seaborn
```

### Erreur de fichier CSV
- Vérifier que le fichier existe
- Vérifier les noms des colonnes
- Vérifier l'encodage (UTF-8 recommandé)

### Problème d'affichage graphique
- Installer matplotlib : `pip install matplotlib`
- Sur certains systèmes : `pip install tkinter`

## 📞 Support

Pour toute question ou personnalisation, consultez :
- Les commentaires dans le code
- Les exemples dans `example_usage.py`
- La configuration dans `cobit_tech_mapping.py`

## 📄 Licence

Outil développé par un expert en gouvernance IT pour l'analyse COBIT 2019.
Libre d'utilisation pour des fins professionnelles et éducatives.
